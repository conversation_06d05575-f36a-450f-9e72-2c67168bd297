#!/usr/bin/env python3
import random

# List of Solidity code snippets with their correct interpretations and distractors
questions = [
    {
        "code": "mapping(address => uint256) public balances;",
        "correct": "Declares a public mapping that associates addresses with unsigned integers",
        "wrong1": "Creates a function that maps addresses to balances",
        "wrong2": "Defines a private variable that stores account balances"
    },
    {
        "code": "require(msg.sender == owner);",
        "correct": "Checks if the transaction sender is the contract owner, reverts if false",
        "wrong1": "Sends a message to the contract owner",
        "wrong2": "Requires the owner to send a message to the contract"
    },
    {
        "code": "event Transfer(address indexed from, address indexed to, uint256 value);",
        "correct": "Declares an event that logs transfers with indexed parameters for efficient filtering",
        "wrong1": "Creates a transfer function with three parameters",
        "wrong2": "Defines a struct to store transfer information"
    },
    {
        "code": "function withdraw() external onlyOwner {",
        "correct": "Defines an external function that can only be called by the contract owner",
        "wrong1": "Creates an internal function accessible only within the contract",
        "wrong2": "Declares a public function that anyone can call"
    },
    {
        "code": "modifier onlyOwner() {",
        "correct": "Defines a function modifier that can restrict access to functions",
        "wrong1": "Creates a special constructor that sets the contract owner",
        "wrong2": "Declares a variable that stores the owner's address"
    },
    {
        "code": "address payable public owner;",
        "correct": "Declares a public variable that can store an address that can receive Ether",
        "wrong1": "Creates a function that pays the owner",
        "wrong2": "Defines a constant address that cannot be changed"
    },
    {
        "code": "uint256 private _totalSupply;",
        "correct": "Declares a private unsigned integer variable",
        "wrong1": "Creates a public function that returns the total supply",
        "wrong2": "Defines a constant that cannot be modified"
    },
    {
        "code": "constructor() public {",
        "correct": "Defines a function that runs only once when the contract is deployed",
        "wrong1": "Creates a public function that can be called by anyone",
        "wrong2": "Declares a variable that stores the contract creator's address"
    },
    {
        "code": "fallback() external payable {",
        "correct": "Defines a function that executes when the contract receives Ether without data",
        "wrong1": "Creates an error handling mechanism for failed transactions",
        "wrong2": "Declares a function that automatically refunds Ether"
    },
    {
        "code": "using SafeMath for uint256;",
        "correct": "Attaches the SafeMath library functions to the uint256 type",
        "wrong1": "Imports the SafeMath contract into the current contract",
        "wrong2": "Declares that the contract inherits from SafeMath"
    },
    {
        "code": "contract Token is ERC20 {",
        "correct": "Defines a contract named Token that inherits from ERC20",
        "wrong1": "Creates a token variable of type ERC20",
        "wrong2": "Declares a function that converts tokens to ERC20"
    },
    {
        "code": "bytes32 public constant DOMAIN_TYPEHASH = keccak256(\"EIP712Domain(string name,uint256 chainId,address verifyingContract)\");",
        "correct": "Declares a constant hash used for EIP-712 domain separation",
        "wrong1": "Creates a function that hashes domain names",
        "wrong2": "Defines a variable that stores the contract's domain name"
    },
    {
        "code": "function transferFrom(address sender, address recipient, uint256 amount) public returns (bool) {",
        "correct": "Defines a function that transfers tokens from one address to another",
        "wrong1": "Creates a private function that can only be called by the contract owner",
        "wrong2": "Declares an event that is emitted when tokens are transferred"
    },
    {
        "code": "revert(\"Not enough balance\");",
        "correct": "Reverts the transaction with an error message",
        "wrong1": "Returns a value to the calling function",
        "wrong2": "Logs an error message to the blockchain"
    },
    {
        "code": "uint8 public decimals = 18;",
        "correct": "Declares a public variable that specifies the token's decimal precision",
        "wrong1": "Creates a function that converts between decimal values",
        "wrong2": "Defines a constant that cannot be changed after deployment"
    },
    {
        "code": "emit Approval(owner, spender, amount);",
        "correct": "Triggers an event to log an approval on the blockchain",
        "wrong1": "Calls a function named Approval with three parameters",
        "wrong2": "Creates a new approval object in storage"
    },
    {
        "code": "function balanceOf(address account) public view returns (uint256) {",
        "correct": "Defines a view function that returns the balance of an address without modifying state",
        "wrong1": "Creates a function that transfers the balance to an account",
        "wrong2": "Declares a variable that stores account balances"
    },
    {
        "code": "pragma solidity ^0.8.0;",
        "correct": "Specifies the compiler version to use for the contract",
        "wrong1": "Imports the Solidity standard library",
        "wrong2": "Sets the gas limit for contract deployment"
    },
    {
        "code": "interface IERC20 {",
        "correct": "Declares an interface that other contracts can implement",
        "wrong1": "Creates a new instance of the ERC20 contract",
        "wrong2": "Defines a function that checks if a contract is ERC20 compliant"
    },
    {
        "code": "struct Proposal {",
        "correct": "Defines a custom data structure with multiple fields",
        "wrong1": "Creates a function that generates proposals",
        "wrong2": "Declares a variable that stores a single proposal"
    },
    {
        "code": "assembly { size := extcodesize(account) }",
        "correct": "Uses inline assembly to get the size of the code at an address",
        "wrong1": "Creates a new assembly contract",
        "wrong2": "Defines a function that assembles contract code"
    },
    {
        "code": "function _mint(address account, uint256 amount) internal {",
        "correct": "Defines an internal function that creates new tokens",
        "wrong1": "Creates a public function that anyone can call",
        "wrong2": "Declares a variable that stores minting information"
    },
    {
        "code": "receive() external payable {",
        "correct": "Defines a function that executes when the contract receives Ether",
        "wrong1": "Creates a function that sends Ether to another address",
        "wrong2": "Declares a variable that stores received Ether"
    },
    {
        "code": "uint256 public constant MAX_SUPPLY = 1000000 * 10**18;",
        "correct": "Declares a constant that represents the maximum token supply with decimals",
        "wrong1": "Creates a function that calculates the maximum supply",
        "wrong2": "Defines a variable that can be changed by the owner"
    },
    {
        "code": "function approve(address spender, uint256 amount) public returns (bool) {",
        "correct": "Defines a function that allows another address to spend tokens on behalf of the caller",
        "wrong1": "Creates a function that approves new token creation",
        "wrong2": "Declares an event that is emitted when approval is granted"
    },
    {
        "code": "library SafeMath {",
        "correct": "Defines a library of functions that can be used by other contracts",
        "wrong1": "Creates a new instance of the SafeMath contract",
        "wrong2": "Imports the SafeMath module from another file"
    },
    {
        "code": "enum State { Created, Locked, Inactive }",
        "correct": "Defines a custom type with a fixed set of values",
        "wrong1": "Creates a function that manages state transitions",
        "wrong2": "Declares a variable that stores the contract's current state"
    },
    {
        "code": "function safeTransfer(IERC20 token, address to, uint256 value) internal {",
        "correct": "Defines an internal function that safely transfers ERC20 tokens",
        "wrong1": "Creates a public function that anyone can call",
        "wrong2": "Declares an event that is emitted when tokens are transferred safely"
    },
    {
        "code": "bytes4 private constant _INTERFACE_ID_ERC721 = 0x80ac58cd;",
        "correct": "Declares a constant that represents the ERC721 interface identifier",
        "wrong1": "Creates a function that checks interface compatibility",
        "wrong2": "Defines a variable that stores the contract's interface ID"
    },
    {
        "code": "function supportsInterface(bytes4 interfaceId) public view returns (bool) {",
        "correct": "Defines a function that checks if the contract implements a specific interface",
        "wrong1": "Creates a function that adds support for new interfaces",
        "wrong2": "Declares a variable that stores supported interfaces"
    }
]

def run_quiz():
    """Run the Solidity code quiz."""
    print("Welcome to the Solidity Code Quiz!")
    print("I'll show you a line of Solidity code, and you need to select the correct interpretation.")
    print("Let's begin!\n")
    
    # Shuffle the questions to get random order
    random.shuffle(questions)
    
    # Select the first question after shuffling
    question = questions[0]
    
    print(f"Solidity code: {question['code']}\n")
    
    # Create a list of answers (1 correct, 2 wrong) and shuffle them
    answers = [
        {"text": question["correct"], "is_correct": True},
        {"text": question["wrong1"], "is_correct": False},
        {"text": question["wrong2"], "is_correct": False}
    ]
    random.shuffle(answers)
    
    # Display the multiple choice options
    for i, answer in enumerate(answers, 1):
        print(f"{i}. {answer['text']}")
    
    # Get user's answer
    while True:
        try:
            user_choice = int(input("\nEnter your choice (1-3): "))
            if 1 <= user_choice <= 3:
                break
            else:
                print("Please enter a number between 1 and 3.")
        except ValueError:
            print("Please enter a valid number.")
    
    # Check if the answer is correct
    if answers[user_choice - 1]["is_correct"]:
        print("\nCorrect! Well done!")
    else:
        correct_answer = next(i+1 for i, a in enumerate(answers) if a["is_correct"])
        print(f"\nIncorrect. The correct answer is {correct_answer}.")
    
    print("\nThanks for playing the Solidity Code Quiz!")

if __name__ == "__main__":
    run_quiz()