{"name": "trading_bot", "version": "1.0.0", "description": "Flash Loan Arbitrage Trading Bot", "main": "index.js", "scripts": {"test": "npx hardhat test", "test:fork": "npx hardhat test --network hardhat", "compile": "npx hardhat compile", "deploy": "npx hardhat run scripts/deploy.js --network mainnet", "start": "node scripts/arbitrage.js"}, "keywords": ["blockchain", "arbitrage", "flash-loan", "uniswap"], "author": "Your Name", "license": "MIT", "devDependencies": {"@nomicfoundation/hardhat-toolbox": "^5.0.0", "@nomicfoundation/hardhat-ethers": "^3.0.8", "@typechain/hardhat": "^9.1.0", "hardhat": "^2.22.2", "chai": "^4.3.10", "typechain": "^8.3.2"}, "dependencies": {"@openzeppelin/contracts": "^5.0.2", "@uniswap/v3-periphery": "^1.4.4", "dotenv": "^16.4.5", "ethers": "^6.13.5"}}