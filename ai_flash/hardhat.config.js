require("@nomicfoundation/hardhat-toolbox");
require("dotenv").config();

const { MAINNET_URL, PRIVATE_KEY, ETHERSCAN_API_KEY } = process.env;

module.exports = {
  solidity: {
    version: "0.8.20",
    settings: {
      viaIR: true,
      optimizer: {
        enabled: true,
        runs: 200,
      },
    },
  },
  networks: {
    hardhat: {
      forking: {
        url: MAINNET_URL,
        enabled: !!MAINNET_URL,
        blockNumber: ********, // Update to a recent block if needed
      },
      gasPrice: 50_000_000_000, // 50 gwei for testing
      accounts: {
        mnemonic: "test test test test test test test test test test test junk",
        count: 10,
      },
    },
    mainnet: {
      url: MAINNET_URL,
      accounts:[ PRIVATE_KEY ],
      chainId: 1,
    },
  },
  etherscan: {
    apiKey: ETHERSCAN_API_KEY,
  },
  mocha: {
    timeout: 40000,
  },
};