const hre = require("hardhat");
const { ethers } = hre;

// Import ethers library for direct access to functions
let ethersLib;
try {
    ethersLib = require("ethers");
    console.log("Using ethers.js library directly");
} catch (error) {
    console.log("Could not import ethers library directly: " + error.message);
}

// Helper function for parsing units with better error handling
function parseUnits(value, unit) {
    console.log(`Parsing units: ${value} ${unit}`);
    try {
        // Try ethers v6 approach first
        if (ethersLib && typeof ethersLib.parseUnits === "function") {
            console.log("Using ethers.parseUnits (v6)");
            return ethersLib.parseUnits(value.toString(), unit);
        }
        // Then try ethers v5 approach
        else if (ethers.utils && typeof ethers.utils.parseUnits === "function") {
            console.log("Using ethers.utils.parseUnits (v5)");
            return ethers.utils.parseUnits(value.toString(), unit);
        }
        // Then try direct ethers approach (could be v6)
        else if (typeof ethers.parseUnits === "function") {
            console.log("Using ethers.parseUnits directly");
            return ethers.parseUnits(value.toString(), unit);
        }
        // Fallback to manual conversion
        else {
            console.log("Using fallback conversion");
            if (unit === "gwei") {
                return BigInt(Math.floor(Number(value) * 10 ** 9));
            } else if (unit === "ether" || unit === 18) {
                return BigInt(Math.floor(Number(value) * 10 ** 18));
            } else {
                const unitNum = typeof unit === "number" ? unit : Number(unit);
                return BigInt(Math.floor(Number(value) * 10 ** unitNum));
            }
        }
    } catch (error) {
        console.error(`Error in parseUnits: ${error.message}`);
        console.error(`Value: ${value}, Unit: ${unit}`);

        // Fallback for common units
        if (unit === "gwei") {
            return BigInt(Math.floor(Number(value) * 10 ** 9));
        } else if (unit === "ether" || unit === 18) {
            return BigInt(Math.floor(Number(value) * 10 ** 18));
        } else {
            const unitNum = typeof unit === "number" ? unit : Number(unit);
            return BigInt(Math.floor(Number(value) * 10 ** unitNum));
        }
    }
}

require("dotenv").config();

// Log ethers version for debugging
const ethersVersion = ethers.version || "unknown";
console.log(`Using ethers.js version: ${ethersVersion}`);

// Check if we're using ethers v5 or v6
const isEthersV5 = !!(ethers.utils && ethers.utils.parseUnits);
const isEthersV6 = !!ethers.parseUnits;
console.log(`Detected ethers version type: ${isEthersV5 ? "v5" : isEthersV6 ? "v6" : "unknown"}`);

// Import configuration
const CONFIG_FILE = require("../config/arbitrage.config.js");

// Configuration
const CONFIG = {
    LENDING_POOL: process.env.LENDING_POOL,
    CONTRACT_ADDRESS: process.env.CONTRACT_ADDRESS,
    MIN_PROFIT_USD: ethers.parseUnits("100", 6),
    MAX_GAS_PRICE: ethers.parseUnits(CONFIG_FILE.GAS.MAX_GAS_PRICE.toString(), "gwei"),
    MAX_PRICE_IMPACT: CONFIG_FILE.TRADING.MAX_PRICE_IMPACT / 100,
    GAS_LIMIT: CONFIG_FILE.GAS.GAS_LIMIT,
    SCAN_INTERVAL: CONFIG_FILE.MONITORING.PRICE_CHECK_INTERVAL,
    PROFIT_MULTIPLIER: CONFIG_FILE.TRADING.PROFIT_MULTIPLIER,
};

// Token definitions
const TOKENS = {
    WETH: { address: "******************************************", decimals: 18 },
    DAI: { address: "******************************************", decimals: 18 },
    USDC: { address: "******************************************", decimals: 6 },
};

// Corrected DEX routers
const DEX_ROUTERS = {
    UniswapV3: "******************************************", // Corrected address
    SushiSwap: "******************************************",
};

const FEE_TIERS = [500, 3000, 10000];

// Contract ABIs
const QUOTER_ABI = [
    // Uniswap V3 Quoter interface
    "function quoteExactInputSingle(address tokenIn, address tokenOut, uint24 fee, uint256 amountIn, uint160 sqrtPriceLimitX96) external view returns (uint256 amountOut)",
    // Alternative signature that might be used in some implementations
    "function quoteExactInputSingle(address tokenIn, address tokenOut, uint24 fee, uint256 amountIn, uint160 sqrtPriceLimitX96) external view returns (uint256 amountOut, uint160 sqrtPriceX96, uint32 initializedTicksCrossed, uint256 gasEstimate)"
];

// We don't need ERC20_ABI as we're using the token metadata directly

// parseUnits function is defined above with comprehensive error handling

// Helper function for formatting units
function formatUnits(value, unit) {
    try {
        if (ethersLib && typeof ethersLib.formatUnits === "function") {
            return ethersLib.formatUnits(value.toString(), unit);
        } else if (ethers.utils && typeof ethers.utils.formatUnits === "function") {
            return ethers.utils.formatUnits(value.toString(), unit);
        } else if (typeof ethers.formatUnits === "function") {
            return ethers.formatUnits(value.toString(), unit);
        } else {
            // Fallback implementation
            if (unit === "gwei") {
                return (Number(value) / 10**9).toString();
            } else if (unit === "ether") {
                return (Number(value) / 10**18).toString();
            } else {
                const unitNum = typeof unit === "number" ? unit : Number(unit);
                return (Number(value) / 10**unitNum).toString();
            }
        }
    } catch (error) {
        console.error(`Error in formatUnits: ${error.message}`);
        return value.toString();
    }
}

function safeNumberConversion(bigIntValue, divisor) {
    try {
        console.log(`Converting ${bigIntValue} / ${divisor}`);

        // Make sure both values are BigInt
        const bigIntValueSafe = BigInt(bigIntValue.toString());
        const divisorSafe = BigInt(divisor.toString());

        // For very large numbers, we need to use string operations
        if (bigIntValueSafe > BigInt(Number.MAX_SAFE_INTEGER)) {
            console.log(`Value too large for direct conversion: ${bigIntValueSafe}`);

            // Convert to string and do manual division
            const stringValue = bigIntValueSafe.toString();
            const divisorString = divisorSafe.toString();

            // Simple string-based division for large numbers
            // This is a simplification and might not be accurate for all cases
            const decimalPlaces = divisorString.length - 1;
            const wholePart = stringValue.slice(0, -decimalPlaces) || '0';
            const decimalPart = stringValue.slice(-decimalPlaces) || '0';

            console.log(`Whole part: ${wholePart}, Decimal part: ${decimalPart}`);
            return parseFloat(`${wholePart}.${decimalPart}`);
        }

        // For smaller numbers, we can do the division in BigInt and then convert
        // We multiply by 1000 first to preserve some decimal places
        const resultScaled = (bigIntValueSafe * BigInt(1000)) / divisorSafe;
        const result = Number(resultScaled) / 1000;

        console.log(`Conversion result: ${result}`);
        return result;
    } catch (error) {
        console.error(`Error in safeNumberConversion: ${error.message}`);
        return 0;
    }
}

async function getPriceFromQuoter(quoter, tokenIn, tokenOut, amountIn, fee) {
    try {
        // Use callStatic to make a read-only call
        console.log(`Getting quote for ${tokenIn} to ${tokenOut} with amount ${amountIn} and fee ${fee}`);

        try {
            // First try with the view function that returns just the amount
            const amountOut = await quoter.quoteExactInputSingle(
                tokenIn,
                tokenOut,
                fee,
                amountIn,
                0 // sqrtPriceLimitX96
            );

            console.log(`Quote received: ${amountOut}`);
            return amountOut;
        } catch (firstError) {
            console.log(`First quote attempt failed: ${firstError.message}. Trying alternative method...`);

            // Try the alternative function that returns multiple values
            const result = await quoter.quoteExactInputSingle(
                tokenIn,
                tokenOut,
                fee,
                amountIn,
                0 // sqrtPriceLimitX96
            );

            // Handle different return types
            if (Array.isArray(result) || typeof result === 'object') {
                // If it's an array or object with multiple return values
                const amountOut = result.amountOut || result[0];
                console.log(`Quote received (alternative method): ${amountOut}`);
                return amountOut;
            } else {
                // If it's a single value
                console.log(`Quote received (direct): ${result}`);
                return result;
            }
        }
    } catch (error) {
        console.error(`Quoter error: ${error.message}`);
        // Return a very small amount instead of null to avoid errors in calculations
        return ethers.parseUnits("1", "wei"); // 1 wei, effectively zero
    }
}

async function calculateProfitability(amountIn, amountOut, tokenDecimals, gasPrice) {
    try {
        console.log(`Calculating profitability with:`);
        console.log(`- amountIn: ${amountIn} (type: ${typeof amountIn})`);
        console.log(`- amountOut: ${amountOut} (type: ${typeof amountOut})`);

        // Convert to BigInt if they're not already
        const amountInBigInt = BigInt(amountIn.toString());
        const amountOutBigInt = BigInt(amountOut.toString());
        const gasLimitBigInt = BigInt(CONFIG.GAS_LIMIT);
        const gasPriceBigInt = BigInt(gasPrice.toString());

        // Check if we need to adjust for decimal differences
        // This is important when comparing tokens with different decimal places
        console.log(`Token decimals: ${tokenDecimals}`);

        // Detect the likely decimal places of the output token based on its magnitude
        let outputDecimals = tokenDecimals; // Default assumption: same decimals
        let adjustedAmountOut = amountOutBigInt;

        // If input is 18 decimals but output looks like 6 decimals
        if (tokenDecimals === 18 && amountOutBigInt < 10n**10n) {
            outputDecimals = 6;
            console.log(`Detected likely 6 decimal token output (e.g., USDC/USDT)`);
            console.log(`Adjusting for decimal mismatch (6 vs 18)`);
            adjustedAmountOut = amountOutBigInt * 10n**12n;
            console.log(`Adjusted amountOut: ${adjustedAmountOut}`);
        }
        // If input is 6 decimals but output looks like 18 decimals
        else if (tokenDecimals === 6 && amountOutBigInt > 10n**10n) {
            outputDecimals = 18;
            console.log(`Detected likely 18 decimal token output (e.g., ETH/DAI)`);
            console.log(`No adjustment needed for 6 -> 18 decimal conversion`);
        }

        console.log(`Input token decimals: ${tokenDecimals}, Output token decimals: ${outputDecimals}`);


        // Calculate profit (adjustedAmountOut - amountIn)
        const profit = adjustedAmountOut - amountInBigInt;
        console.log(`Calculated profit: ${profit}`);

        // If profit is negative, return early with isProfitable = false
        if (profit <= 0) {
            console.log(`Negative or zero profit detected: ${profit}. Skipping further calculations.`);
            return {
                isProfitable: false,
                profitUsd: 0,
                gasCostUsd: 0,
                minRequiredProfit: 0
            };
        }

        // Calculate gas cost (gasPrice * gasLimit)
        const gasCostEth = gasPriceBigInt * gasLimitBigInt;
        console.log(`Calculated gas cost: ${gasCostEth}`);

        // Get ETH price
        const ethPrice = await getEthPrice();
        const ethPriceBigInt = BigInt(ethPrice.toString());
        console.log(`ETH price: ${ethPriceBigInt}`);

        // Calculate profit in USD using the detected output decimals
        // For USDC (6 decimals) to ETH (18 decimals), we need to adjust the calculation
        let profitUsd;

        if (tokenDecimals === 6 && outputDecimals === 18) {
            // Special case for USDC -> ETH conversion
            // Convert profit to ETH value first
            const profitInEth = Number(profit) / 10**18;
            // Then convert to USD
            const ethPriceUsd = Number(ethPriceBigInt) / 10**8; // Chainlink uses 8 decimals
            profitUsd = profitInEth * ethPriceUsd;
            console.log(`Special calculation for USDC->ETH: ${profitInEth} ETH * $${ethPriceUsd} = $${profitUsd.toFixed(2)}`);
        } else {
            // Standard calculation
            const profitBigInt = profit * ethPriceBigInt;
            const divisorProfit = BigInt(10 ** (tokenDecimals + 18));
            profitUsd = safeNumberConversion(profitBigInt, divisorProfit);
            console.log(`Standard profit calculation: $${profitUsd.toFixed(2)}`);
        }

        console.log(`Profit in USD: $${profitUsd.toFixed(2)}`);

        // We're using outputDecimals in our calculations

        // Double-check if profit is still positive after conversion
        if (profitUsd <= 0) {
            console.log(`Profit in USD is not positive: $${profitUsd.toFixed(2)}. Skipping further calculations.`);
            return {
                isProfitable: false,
                profitUsd: 0,
                gasCostUsd: 0,
                minRequiredProfit: 0
            };
        }

        // Calculate gas cost in USD
        const gasCostBigInt = gasCostEth * ethPriceBigInt;
        const divisorGas = BigInt(10 ** 36);
        const gasCostUsd = safeNumberConversion(gasCostBigInt, divisorGas);
        console.log(`Gas cost in USD: $${gasCostUsd.toFixed(2)}`);

        console.log(`Profit calculation: ${profitUsd.toFixed(2)} USD, Gas cost: ${gasCostUsd.toFixed(2)} USD`);

        // Make sure PROFIT_MULTIPLIER is a number
        const profitMultiplier = typeof CONFIG.PROFIT_MULTIPLIER === 'number' ? CONFIG.PROFIT_MULTIPLIER : 3;
        console.log(`Using profit multiplier: ${profitMultiplier}x`);

        // Calculate minimum required profit (ensure it's at least $0.01)
        let minRequiredProfit = gasCostUsd * profitMultiplier;

        // If gas cost is very low, ensure we have a minimum threshold
        if (minRequiredProfit < 0.01) {
            minRequiredProfit = 0.01; // Minimum $0.01 profit required
            console.log(`Gas cost too low, using minimum profit threshold: $${minRequiredProfit.toFixed(2)}`);
        } else {
            console.log(`Minimum required profit: $${minRequiredProfit.toFixed(2)}`);
        }

        // Sanity check for unrealistically high profits
        // If profit is more than $10,000, it's likely a calculation error or market inefficiency
        if (profitUsd > 10000) {
            console.log(`WARNING: Extremely high profit detected: $${profitUsd.toFixed(2)}. This may be a calculation error.`);
            console.log(`Performing additional verification...`);

            // Calculate the profit as a percentage of the input amount
            const inputValueUsd = await getTokenValueInUsd(amountInBigInt, tokenDecimals, outputDecimals);
            const profitPercentage = (profitUsd / inputValueUsd) * 100;

            console.log(`Input value: $${inputValueUsd.toFixed(2)}, Profit percentage: ${profitPercentage.toFixed(2)}%`);

            // If profit is more than 10% of input value, it's likely too good to be true
            if (profitPercentage > 10) {
                console.log(`Profit percentage exceeds 10% of input value. This is likely a calculation error or market inefficiency.`);
                console.log(`Consider manually verifying this opportunity before executing.`);

                // We'll still return it as profitable, but with a warning flag
                return {
                    isProfitable: profitUsd > minRequiredProfit,
                    profitUsd: profitUsd,
                    gasCostUsd: gasCostUsd,
                    minRequiredProfit: minRequiredProfit,
                    suspiciouslyHighProfit: true
                };
            }
        }



        return {
            isProfitable: profitUsd > minRequiredProfit,
            profitUsd: profitUsd,
            gasCostUsd: gasCostUsd,
            minRequiredProfit: minRequiredProfit,
        };
    } catch (error) {
        console.error(`Error in calculateProfitability: ${error.message}`);
        return {
            isProfitable: false,
            profitUsd: 0,
            gasCostUsd: 0,
            minRequiredProfit: 0,
        };
    }
}

async function getEthPrice() {
    try {
        const chainlink = new ethers.Contract(
            "******************************************", // ETH/USD price feed
            ["function latestAnswer() external view returns (int256)"],
            ethers.provider
        );

        const price = await chainlink.latestAnswer();
        console.log(`Got ETH price from Chainlink: ${price} (${typeof price})`);

        // Make sure we return a BigInt
        return BigInt(price.toString());
    } catch (error) {
        console.error(`Error getting ETH price: ${error.message}`);
        // Return a fallback price of $2000 USD
        return BigInt(2000 * 10**8); // Chainlink uses 8 decimals
    }
}

async function checkAndSetApprovals(tokenAddress, amount, spenderAddress) {
    try {
        console.log(`Checking approvals for token ${tokenAddress}...`);
        const [signer] = await ethers.getSigners();

        // Get token contract
        const tokenContract = await ethers.getContractAt(
            ["function allowance(address,address) view returns (uint256)", "function approve(address,uint256) returns (bool)"],
            tokenAddress,
            signer
        );

        // Check current allowance
        const currentAllowance = await tokenContract.allowance(signer.address, spenderAddress);
        console.log(`Current allowance: ${currentAllowance}`);

        // If allowance is less than amount, approve
        if (currentAllowance < amount) {
            console.log(`Setting approval for ${spenderAddress} to spend ${amount} of token ${tokenAddress}...`);

            // Approve max uint256 to avoid having to approve again
            const maxUint256 = BigInt("0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff");
            const tx = await tokenContract.approve(spenderAddress, maxUint256);

            console.log(`Approval transaction sent: ${tx.hash}`);
            const receipt = await tx.wait();
            console.log(`Approval confirmed in block ${receipt.blockNumber}`);
            return true;
        } else {
            console.log(`Approval already set. Current allowance: ${currentAllowance}`);
            return true;
        }
    } catch (error) {
        console.error(`Error setting approvals: ${error.message}`);
        return false;
    }
}

async function getTokenValueInUsd(amountBigInt, tokenDecimals, outputDecimals = null) {
    try {
        // For simplicity, we'll use a fixed price for tokens
        // In a production environment, you'd want to fetch real-time prices
        let tokenPriceUsd;

        // Use outputDecimals to determine token type if provided
        const effectiveDecimals = outputDecimals || tokenDecimals;

        // Get token addresses for known tokens
        const WETH_ADDRESS = "******************************************".toLowerCase();
        const USDC_ADDRESS = "******************************************".toLowerCase();
        const DAI_ADDRESS = "******************************************".toLowerCase();

        // Get token addresses from the token pairs in the CONFIG
        const tokenAddresses = Object.values(CONFIG.TOKENS || {}).map(token => token.address?.toLowerCase());

        // Determine if this is a stablecoin
        const isStablecoin = tokenAddresses.includes(USDC_ADDRESS) || tokenAddresses.includes(DAI_ADDRESS) || effectiveDecimals === 6;

        // Determine if this is ETH or an ETH-like token
        const isEth = tokenAddresses.includes(WETH_ADDRESS) || effectiveDecimals === 18;

        if (isEth) {
            // This is ETH or an ETH-like token
            const ethPrice = await getEthPrice();
            tokenPriceUsd = Number(ethPrice) / 10**8; // Chainlink uses 8 decimals
            console.log(`Using ETH price: $${tokenPriceUsd.toFixed(2)}`);
        } else if (isStablecoin) {
            // This is USDC, DAI or a stablecoin
            tokenPriceUsd = 1.0; // $1 USD
            console.log(`Using stablecoin price: $${tokenPriceUsd.toFixed(2)}`);
        } else {
            // Default fallback
            tokenPriceUsd = 1.0;
            console.log(`Using default price: $${tokenPriceUsd.toFixed(2)}`);
        }

        // Calculate the value in USD
        const amountInToken = Number(amountBigInt) / 10**tokenDecimals;
        const valueUsd = amountInToken * tokenPriceUsd;

        console.log(`Token value calculation: ${amountInToken} tokens at $${tokenPriceUsd} = $${valueUsd}`);

        return valueUsd;
    } catch (error) {
        console.error(`Error calculating token value: ${error.message}`);
        return 1000; // Default fallback value
    }
}

async function findArbitrageOpportunities(quoter, tokenPair, amountIn) {
    const opportunities = [];
    for (const fee of FEE_TIERS) {
        for (const [dex1Name, dex1Router] of Object.entries(DEX_ROUTERS)) {
            for (const [dex2Name, dex2Router] of Object.entries(DEX_ROUTERS)) {
                if (dex1Name === dex2Name) continue;

                const price1 = await getPriceFromQuoter(
                    quoter,
                    tokenPair.tokenIn.address,
                    tokenPair.tokenOut.address,
                    amountIn,
                    fee
                );

                const price2 = await getPriceFromQuoter(
                    quoter,
                    tokenPair.tokenIn.address,
                    tokenPair.tokenOut.address,
                    amountIn,
                    fee
                );

                if (!price1 || !price2) continue;

                const feeData = await ethers.provider.getFeeData();
                const gasPrice = feeData.gasPrice || feeData.maxFeePerGas;

                let bestPrice = price1 > price2 ? price1 : price2;

                const profitability = await calculateProfitability(
                    amountIn,
                    bestPrice,
                    tokenPair.tokenIn.decimals,
                    gasPrice
                );

                if (profitability.isProfitable) {
                    opportunities.push({
                        tokenIn: tokenPair.tokenIn.address,
                        tokenOut: tokenPair.tokenOut.address,
                        amountIn: amountIn,
                        buyRouter: dex1Router,
                        sellRouter: dex2Router,
                        fee: fee,
                        profitUsd: profitability.profitUsd,
                        gasCostUsd: profitability.gasCostUsd,
                        dexPair: `${dex1Name} -> ${dex2Name}`,
                    });
                }
            }
        }
    }
    return opportunities;
}

async function main() {
    const networkName = hre.network.name;
    console.log(`Running on network: ${networkName}`);

    if (networkName === "hardhat") {
        console.warn("\nWARNING: You are running on the Hardhat test network with test accounts.");
        console.warn("To use your real account, run with: npx hardhat run scripts/monitor.js --network mainnet\n");
    }

    const [signer] = await ethers.getSigners();
    console.log(`Running with account: ${signer.address}`);

    if (!CONFIG.CONTRACT_ADDRESS) {
        console.error("\nERROR: CONTRACT_ADDRESS is not set in your environment variables.");
        console.error("Please set it in your .env file or export it as an environment variable:\n");
        console.error("export CONTRACT_ADDRESS=your_deployed_contract_address\n");
        process.exit(1);
    }

    console.log(`Using contract at: ${CONFIG.CONTRACT_ADDRESS}`);

    // Verify PROFIT_MULTIPLIER is properly set
    if (typeof CONFIG.PROFIT_MULTIPLIER !== 'number') {
        console.warn(`WARNING: PROFIT_MULTIPLIER is not a number (${CONFIG.PROFIT_MULTIPLIER}), using default value of 3`);
        CONFIG.PROFIT_MULTIPLIER = 3;
    } else {
        console.log(`Using PROFIT_MULTIPLIER: ${CONFIG.PROFIT_MULTIPLIER}x`);
    }

    let maxGasPrice;
    if (typeof CONFIG.MAX_GAS_PRICE === "bigint") {
        maxGasPrice = CONFIG.MAX_GAS_PRICE;
    } else {
        maxGasPrice = parseUnits(CONFIG.MAX_GAS_PRICE.toString(), "gwei");
        console.log(`Converted ${CONFIG.MAX_GAS_PRICE} gwei to ${maxGasPrice} wei`);
    }

    const arbitrage = await ethers.getContractAt("FlashLoanArbitrage", CONFIG.CONTRACT_ADDRESS, signer);

    // Check if the contract has a valid LENDING_POOL address
    const lendingPoolAddress = await arbitrage.LENDING_POOL();
    console.log(`Contract's LENDING_POOL address: ${lendingPoolAddress}`);

    if (lendingPoolAddress === ethers.ZeroAddress) {
        console.error("\nERROR: The contract's LENDING_POOL address is set to the zero address.");
        console.error("Flash loans will not work without a valid LENDING_POOL address.");
        console.error("Please deploy a new contract with a valid LENDING_POOL address.\n");
    } else {
        console.log(`Using LENDING_POOL at: ${lendingPoolAddress}`);
    }

    // Use standalone ethersLib for quoter
    // Use the hardhat provider which is already connected to the network
    console.log("Using hardhat provider for quoter contract");

    // Initialize the Uniswap V3 Quoter contract
    const QUOTER_ADDRESS = "******************************************";
    const quoter = await ethers.getContractAt(QUOTER_ABI, QUOTER_ADDRESS);
    console.log(`Quoter contract initialized at ${QUOTER_ADDRESS}`);

    console.log("Starting arbitrage bot...");

    try {
        while (true) {
            try {
                const feeData = await ethers.provider.getFeeData();
                const gasPrice = feeData.gasPrice || feeData.maxFeePerGas;

                // Format gas prices for display using direct conversion
                const currentGasGwei = Number(gasPrice) / 10**9;
                const maxGasGwei = Number(maxGasPrice) / 10**9;

                console.log(`Current gas price: ${currentGasGwei.toFixed(6)} gwei (Max: ${maxGasGwei.toFixed(2)} gwei)`);

                if (gasPrice > maxGasPrice) {
                    console.log("Gas price too high, waiting...");
                    await new Promise((resolve) => setTimeout(resolve, CONFIG.SCAN_INTERVAL));
                    continue;
                }

                for (const [tokenInName, tokenIn] of Object.entries(TOKENS)) {
                    for (const [tokenOutName, tokenOut] of Object.entries(TOKENS)) {
                        if (tokenIn.address === tokenOut.address) continue;

                        console.log(`Checking ${tokenInName} -> ${tokenOutName}`);

                        // Calculate amount in (1000 tokens with proper decimals)
                        let amountIn;
                        try {
                            amountIn = parseUnits("1000", tokenIn.decimals);
                            console.log(`Using amount: ${amountIn} (${tokenIn.decimals} decimals)`);
                        } catch (error) {
                            console.error(`Error parsing units: ${error.message}`);
                            // Fallback: calculate manually
                            amountIn = BigInt(1000) * BigInt(10 ** tokenIn.decimals);
                            console.log(`Using fallback amount: ${amountIn}`);
                        }
                        const tokenPair = { tokenIn, tokenOut };

                        const opportunities = await findArbitrageOpportunities(quoter, tokenPair, amountIn);

                        for (const opp of opportunities) {
                            console.log(`\nProfit opportunity found: ${tokenInName} -> ${tokenOutName}`);
                            console.log(`Route: ${opp.dexPair}`);
                            console.log(`Profit: $${opp.profitUsd.toFixed(2)} (Gas: $${opp.gasCostUsd.toFixed(2)})`);

                            // Make sure minRequiredProfit is a number before calling toFixed
                            const minProfitDisplay = typeof opp.minRequiredProfit === 'number' ?
                                opp.minRequiredProfit.toFixed(2) :
                                '0.00';

                            // Use a default value of 3 if PROFIT_MULTIPLIER is undefined
                            const profitMultiplier = typeof CONFIG.PROFIT_MULTIPLIER === 'number' ?
                                CONFIG.PROFIT_MULTIPLIER :
                                3;

                            console.log(`Required profit (${profitMultiplier}x gas): $${minProfitDisplay}`);

                            // Display warning for suspiciously high profits
                            if (opp.suspiciouslyHighProfit) {
                                console.log(`⚠️ WARNING: This profit appears unusually high and may be a calculation error.`);
                                console.log(`⚠️ Please verify this opportunity manually before executing.`);
                            }

                            try {
                                // Check and set token approvals first
                                await checkAndSetApprovals(opp.tokenIn, opp.amountIn, CONFIG.CONTRACT_ADDRESS);

                                const trades = [
                                    {
                                        router: opp.buyRouter,
                                        tokenIn: opp.tokenIn,
                                        tokenOut: opp.tokenOut,
                                        fee: opp.fee,
                                        amountIn: opp.amountIn,
                                        amountOutMin: 0,
                                    },
                                    {
                                        router: opp.sellRouter,
                                        tokenIn: opp.tokenOut,
                                        tokenOut: opp.tokenIn,
                                        fee: opp.fee,
                                        amountIn: 0,
                                        amountOutMin: opp.amountIn,
                                    },
                                ];

                                // Add slippage tolerance to ensure the trade doesn't fail due to price changes
                                const slippageTolerance = 0.05; // 5%
                                trades[1].amountOutMin = BigInt(Number(opp.amountIn) * (1 - slippageTolerance));

                                // Prepare transaction options for ethers.js v6
                                const txOptions = {
                                    gasLimit: CONFIG.GAS_LIMIT
                                };

                                // Get current gas price
                                const feeData = await ethers.provider.getFeeData();

                                // For ethers.js v6, we need to explicitly set the gas price
                                if (feeData.gasPrice) {
                                    // Use the current gas price from the provider
                                    txOptions.gasPrice = feeData.gasPrice;
                                    console.log(`Using current gas price: ${Number(feeData.gasPrice) / 10**9} gwei`);
                                } else if (feeData.maxFeePerGas) {
                                    // Use EIP-1559 fee structure
                                    txOptions.maxFeePerGas = feeData.maxFeePerGas;
                                    txOptions.maxPriorityFeePerGas = feeData.maxPriorityFeePerGas;
                                    console.log(`Using EIP-1559 fees: ${Number(feeData.maxFeePerGas) / 10**9} gwei (max), ${Number(feeData.maxPriorityFeePerGas) / 10**9} gwei (priority)`);
                                } else {
                                    // Fallback to a reasonable gas price
                                    const fallbackGasPrice = BigInt(50 * 10**9); // 50 gwei
                                    txOptions.gasPrice = fallbackGasPrice;
                                    console.log(`Using fallback gas price: 50 gwei`);
                                }

                                console.log(`Executing arbitrage with options: ${JSON.stringify({
                                    gasLimit: txOptions.gasLimit.toString(),
                                    gasPrice: txOptions.gasPrice ? (Number(txOptions.gasPrice) / 10**9).toString() + ' gwei' : undefined,
                                    maxFeePerGas: txOptions.maxFeePerGas ? (Number(txOptions.maxFeePerGas) / 10**9).toString() + ' gwei' : undefined,
                                    maxPriorityFeePerGas: txOptions.maxPriorityFeePerGas ? (Number(txOptions.maxPriorityFeePerGas) / 10**9).toString() + ' gwei' : undefined
                                })}`);

                                // Debug log the trade parameters
                                console.log(`\nExecuting arbitrage with the following parameters:`);
                                console.log(`- Token In: ${opp.tokenIn}`);
                                console.log(`- Amount In: ${opp.amountIn}`);
                                console.log(`- First Trade: ${opp.tokenIn} -> ${opp.tokenOut} via ${opp.buyRouter}`);
                                console.log(`- Second Trade: ${opp.tokenOut} -> ${opp.tokenIn} via ${opp.sellRouter}`);
                                console.log(`- Fee: ${opp.fee}`);
                                console.log(`- Minimum Amount Out: ${trades[1].amountOutMin}`);

                                // Check if we're using flash loans
                                const tokenContract = await ethers.getContractAt(
                                    ["function balanceOf(address) view returns (uint256)"],
                                    opp.tokenIn,
                                    signer
                                );

                                const signerBalance = await tokenContract.balanceOf(signer.address);
                                console.log(`Your balance of token ${opp.tokenIn}: ${signerBalance}`);

                                if (signerBalance < opp.amountIn) {
                                    console.log(`\nUsing flash loan for this trade since your balance (${signerBalance}) is less than required (${opp.amountIn}).`);
                                    console.log(`This is normal for flash loan arbitrage - you don't need to have the tokens upfront.`);
                                }

                                // Execute the arbitrage
                                console.log(`\nSending transaction...`);
                                const tx = await arbitrage.executeArbitrage(opp.tokenIn, opp.amountIn, trades, txOptions);

                                console.log(`Transaction sent: ${tx.hash}`);
                                console.log(`Waiting for confirmation...`);

                                const receipt = await tx.wait();
                                console.log(`\nTrade executed successfully!`);
                                console.log(`- Block: ${receipt.blockNumber}`);
                                console.log(`- Gas Used: ${receipt.gasUsed}`);
                                console.log(`- Effective Gas Price: ${formatUnits(receipt.effectiveGasPrice || receipt.gasPrice, "gwei")} gwei`);
                                console.log(`- Total Cost: ${formatUnits((receipt.gasUsed * (receipt.effectiveGasPrice || receipt.gasPrice)), "ether")} ETH`);
                            } catch (error) {
                                console.error(`\nExecution failed: ${error.message}`);

                                // Try to get more detailed error information
                                if (error.transaction) {
                                    console.error(`\nTransaction details:`);
                                    console.error(`- From: ${error.transaction.from}`);
                                    console.error(`- To: ${error.transaction.to}`);
                                    console.error(`- Gas Limit: ${error.transaction.gasLimit || 'unknown'}`);
                                    console.error(`- Gas Price: ${error.transaction.gasPrice ? formatUnits(error.transaction.gasPrice, "gwei") + ' gwei' : 'unknown'}`);
                                }

                                if (error.receipt) {
                                    console.error(`\nReceipt details:`);
                                    console.error(`- Block: ${error.receipt.blockNumber}`);
                                    console.error(`- Gas Used: ${error.receipt.gasUsed}`);
                                    console.error(`- Status: ${error.receipt.status === 0 ? 'Failed (0)' : error.receipt.status}`);
                                }

                                console.error(`\nPossible reasons for failure:`);
                                console.error(`1. Insufficient token approvals`);
                                console.error(`2. Price slippage exceeded tolerance`);
                                console.error(`3. Insufficient funds for gas`);
                                console.error(`4. Contract doesn't have required permissions`);
                                console.error(`5. Blockchain congestion causing transaction to fail`);
                            }
                        }
                    }
                }

                await new Promise((resolve) => setTimeout(resolve, CONFIG.SCAN_INTERVAL));
                console.log("Scanning next cycle...");
            } catch (error) {
                console.error(`Main loop error: ${error.message}`);
                await new Promise((resolve) => setTimeout(resolve, CONFIG.SCAN_INTERVAL));
            }
        }
    } catch (error) {
        console.error(`\nCritical error in arbitrage bot: ${error.message}`);
        console.error(error.stack);
        process.exit(1);
    }
}

// Execute the main function
main().catch(console.error);