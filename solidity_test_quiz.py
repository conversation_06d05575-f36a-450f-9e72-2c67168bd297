#!/usr/bin/env python3
import random

# List of Solidity code snippets with their correct JavaScript test lines and distractors
questions = [
    {
        "code": "function transfer(address to, uint256 amount) public returns (bool) {",
        "correct": "expect(await token.transfer(recipient.address, 100)).to.emit(token, 'Transfer');",
        "wrong1": "await token.transferFrom(owner, recipient, 100);",
        "wrong2": "expect(token.transfer(100, recipient)).to.be.reverted;"
    },
    {
        "code": "modifier onlyOwner() {",
        "correct": "await expect(contract.connect(nonOwner).restrictedFunction()).to.be.revertedWith('Not owner');",
        "wrong1": "expect(await contract.onlyOwner()).to.equal(true);",
        "wrong2": "const owner = await contract.onlyOwner();"
    },
    {
        "code": "event Approval(address indexed owner, address indexed spender, uint256 value);",
        "correct": "await expect(token.approve(spender.address, 100)).to.emit(token, 'Approval');",
        "wrong1": "const approval = await token.Approval(owner, spender, 100);",
        "wrong2": "await token.emit('Approval', owner.address, spender.address, 100);"
    },
    {
        "code": "mapping(address => uint256) public balances;",
        "correct": "expect(await token.balances(user.address)).to.equal(100);",
        "wrong1": "await token.balances.set(user.address, 100);",
        "wrong2": "const mapping = await token.getMapping('balances');"
    },
    {
        "code": "require(msg.sender == owner, 'Not owner');",
        "correct": "await expect(contract.connect(nonOwner).restrictedFunction()).to.be.revertedWith('Not owner');",
        "wrong1": "expect(contract.owner()).to.equal(msg.sender);",
        "wrong2": "await contract.require(owner.address);"
    },
    {
        "code": "function balanceOf(address account) public view returns (uint256) {",
        "correct": "expect(await token.balanceOf(user.address)).to.equal(100);",
        "wrong1": "await token.balanceOf.set(user.address, 100);",
        "wrong2": "const balance = token.getBalance(user);"
    },
    {
        "code": "constructor(string memory name_, string memory symbol_) {",
        "correct": "const token = await Token.deploy('MyToken', 'MTK');",
        "wrong1": "await token.constructor('MyToken', 'MTK');",
        "wrong2": "token.initialize('MyToken', 'MTK');"
    },
    {
        "code": "function approve(address spender, uint256 amount) public returns (bool) {",
        "correct": "await token.approve(spender.address, 1000);",
        "wrong1": "expect(token.approve(1000, spender)).to.be.true;",
        "wrong2": "await token.setApproval(spender, 1000);"
    },
    {
        "code": "uint8 public decimals = 18;",
        "correct": "expect(await token.decimals()).to.equal(18);",
        "wrong1": "await token.setDecimals(18);",
        "wrong2": "const decimals = token.getDecimals();"
    },
    {
        "code": "function mint(address to, uint256 amount) public onlyOwner {",
        "correct": "await token.connect(owner).mint(recipient.address, 1000);",
        "wrong1": "expect(token.mint(1000, recipient)).to.be.reverted;",
        "wrong2": "await token.createTokens(recipient, 1000);"
    },
    {
        "code": "function transferFrom(address from, address to, uint256 amount) public returns (bool) {",
        "correct": "await token.connect(spender).transferFrom(owner.address, recipient.address, 100);",
        "wrong1": "expect(token.transfer(owner, recipient, 100)).to.be.true;",
        "wrong2": "await token.moveTokens(owner, recipient, 100);"
    },
    {
        "code": "function burn(uint256 amount) public {",
        "correct": "await token.connect(holder).burn(1000);",
        "wrong1": "expect(token.burn()).to.equal(1000);",
        "wrong2": "await token.destroyTokens(1000, holder);"
    },
    {
        "code": "function pause() public onlyOwner {",
        "correct": "await contract.connect(owner).pause();",
        "wrong1": "expect(contract.pause).to.be.true;",
        "wrong2": "await contract.setPaused(true);"
    },
    {
        "code": "function totalSupply() public view returns (uint256) {",
        "correct": "expect(await token.totalSupply()).to.equal(1000000);",
        "wrong1": "await token.setTotalSupply(1000000);",
        "wrong2": "const supply = token.getSupply();"
    },
    {
        "code": "receive() external payable {",
        "correct": "await user.sendTransaction({ to: contract.address, value: ethers.utils.parseEther('1.0') });",
        "wrong1": "await contract.receive({ value: 1000 });",
        "wrong2": "expect(contract.receive).to.be.payable;"
    },
    {
        "code": "function withdraw() public onlyOwner {",
        "correct": "await contract.connect(owner).withdraw();",
        "wrong1": "expect(contract.withdraw).to.be.onlyOwner;",
        "wrong2": "await contract.sendFunds(owner);"
    },
    {
        "code": "function setBaseURI(string memory baseURI_) public onlyOwner {",
        "correct": "await nft.connect(owner).setBaseURI('https://example.com/');",
        "wrong1": "expect(nft.baseURI()).to.equal('https://example.com/');",
        "wrong2": "await nft.updateURI('https://example.com/');"
    },
    {
        "code": "function supportsInterface(bytes4 interfaceId) public view returns (bool) {",
        "correct": "expect(await contract.supportsInterface('0x80ac58cd')).to.be.true;",
        "wrong1": "await contract.addInterface('0x80ac58cd');",
        "wrong2": "const interfaces = contract.getInterfaces();"
    },
    {
        "code": "function safeMint(address to, uint256 tokenId) public onlyOwner {",
        "correct": "await nft.connect(owner).safeMint(recipient.address, 1);",
        "wrong1": "expect(nft.mint(recipient, 1)).to.be.safe;",
        "wrong2": "await nft.createToken(1, recipient);"
    },
    {
        "code": "function _beforeTokenTransfer(address from, address to, uint256 tokenId) internal override {",
        "correct": "await expect(token.transferFrom(holder.address, recipient.address, 1)).to.emit(token, 'BeforeTransfer');",
        "wrong1": "await token._beforeTokenTransfer(holder, recipient, 1);",
        "wrong2": "expect(token.beforeTransfer).to.be.called;"
    },
    {
        "code": "function increaseAllowance(address spender, uint256 addedValue) public returns (bool) {",
        "correct": "await token.connect(owner).increaseAllowance(spender.address, 100);",
        "wrong1": "expect(token.allowance(owner, spender)).to.increase.by(100);",
        "wrong2": "await token.addAllowance(spender, 100);"
    },
    {
        "code": "function decreaseAllowance(address spender, uint256 subtractedValue) public returns (bool) {",
        "correct": "await token.connect(owner).decreaseAllowance(spender.address, 50);",
        "wrong1": "expect(token.allowance(owner, spender)).to.decrease.by(50);",
        "wrong2": "await token.subtractAllowance(spender, 50);"
    },
    {
        "code": "function tokenURI(uint256 tokenId) public view returns (string memory) {",
        "correct": "expect(await nft.tokenURI(1)).to.equal('https://example.com/1');",
        "wrong1": "await nft.setTokenURI(1, 'https://example.com/1');",
        "wrong2": "const uri = nft.getURI(1);"
    },
    {
        "code": "function renounceOwnership() public onlyOwner {",
        "correct": "await contract.connect(owner).renounceOwnership();",
        "wrong1": "expect(contract.owner()).to.be.equal(ethers.constants.AddressZero);",
        "wrong2": "await contract.removeOwner();"
    },
    {
        "code": "function transferOwnership(address newOwner) public onlyOwner {",
        "correct": "await contract.connect(owner).transferOwnership(newOwner.address);",
        "wrong1": "expect(contract.owner).to.be.transferred.to(newOwner);",
        "wrong2": "await contract.setOwner(newOwner);"
    },
    {
        "code": "function allowance(address owner, address spender) public view returns (uint256) {",
        "correct": "expect(await token.allowance(owner.address, spender.address)).to.equal(100);",
        "wrong1": "await token.setAllowance(owner, spender, 100);",
        "wrong2": "const allowed = token.getAllowed(owner, spender);"
    },
    {
        "code": "function symbol() public view returns (string memory) {",
        "correct": "expect(await token.symbol()).to.equal('TKN');",
        "wrong1": "await token.setSymbol('TKN');",
        "wrong2": "const sym = token.getSymbol();"
    },
    {
        "code": "function name() public view returns (string memory) {",
        "correct": "expect(await token.name()).to.equal('MyToken');",
        "wrong1": "await token.setName('MyToken');",
        "wrong2": "const tokenName = token.getName();"
    },
    {
        "code": "function _mint(address account, uint256 amount) internal {",
        "correct": "await expect(token.connect(owner).mint(account.address, 1000)).to.emit(token, 'Transfer');",
        "wrong1": "await token._mint(account, 1000);",
        "wrong2": "expect(token.internalMint).to.be.called;"
    },
    {
        "code": "function _burn(address account, uint256 amount) internal {",
        "correct": "await expect(token.connect(holder).burn(1000)).to.emit(token, 'Transfer');",
        "wrong1": "await token._burn(holder, 1000);",
        "wrong2": "expect(token.internalBurn).to.be.called;"
    }
]

def run_quiz():
    """Run the Solidity Test Quiz."""
    print("Welcome to the Solidity Testing Quiz!")
    print("I'll show you a piece of Solidity code, and you need to select the correct JavaScript test line.")
    print("Let's begin!\n")
    
    # Shuffle the questions to get random order
    random.shuffle(questions)
    
    # Select the first question after shuffling
    question = questions[0]
    
    print(f"Solidity code: {question['code']}\n")
    print("Which JavaScript test line would correctly test this code?\n")
    
    # Create a list of answers (1 correct, 2 wrong) and shuffle them
    answers = [
        {"text": question["correct"], "is_correct": True},
        {"text": question["wrong1"], "is_correct": False},
        {"text": question["wrong2"], "is_correct": False}
    ]
    random.shuffle(answers)
    
    # Display the multiple choice options
    for i, answer in enumerate(answers, 1):
        print(f"{i}. {answer['text']}")
    
    # Get user's answer
    while True:
        try:
            user_choice = int(input("\nEnter your choice (1-3): "))
            if 1 <= user_choice <= 3:
                break
            else:
                print("Please enter a number between 1 and 3.")
        except ValueError:
            print("Please enter a valid number.")
    
    # Check if the answer is correct
    if answers[user_choice - 1]["is_correct"]:
        print("\nCorrect! Well done!")
    else:
        correct_answer = next(i+1 for i, a in enumerate(answers) if a["is_correct"])
        print(f"\nIncorrect. The correct answer is {correct_answer}.")
    
    print("\nThanks for playing the Solidity Testing Quiz!")

if __name__ == "__main__":
    run_quiz()