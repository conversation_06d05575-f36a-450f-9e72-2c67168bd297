<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/cardLesson"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <TextView
            android:id="@+id/tvLessonTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Lesson Title"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="@color/text_primary" />

        <TextView
            android:id="@+id/tvLessonDescription"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Lesson Description"
            android:textSize="14sp"
            android:textColor="@color/text_secondary"
            android:layout_marginTop="4dp" />

        <io.github.kbiakov.codeview.CodeView
            android:id="@+id/codeView"
            android:layout_width="match_parent"
            android:layout_height="200dp"
            android:layout_marginTop="12dp" />

        <TextView
            android:id="@+id/tvExplanation"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Explanation"
            android:textSize="14sp"
            android:layout_marginTop="12dp"
            android:textColor="@color/text_primary" />

    </LinearLayout>

</androidx.cardview.widget.CardView>
