<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp"
    tools:context=".LessonActivity">

    <TextView
        android:id="@+id/tvLessonTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Lesson Title"
        android:textSize="24sp"
        android:textStyle="bold"
        android:textAlignment="center"
        android:layout_marginBottom="8dp" />

    <TextView
        android:id="@+id/tvLessonDescription"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Lesson Description"
        android:textSize="16sp"
        android:textAlignment="center"
        android:layout_marginBottom="16dp"
        android:textColor="@color/text_secondary" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerViewLessons"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        tools:listitem="@layout/item_lesson" />

    <Button
        android:id="@+id/btnStartQuiz"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Start Quiz"
        android:layout_marginTop="16dp"
        android:enabled="false" />

</LinearLayout>
