<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".QuizActivity">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <TextView
            android:id="@+id/tvQuizProgress"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Question 1 of 5"
            android:textSize="16sp"
            android:textAlignment="center"
            android:layout_marginBottom="16dp"
            android:textColor="@color/primary_blue" />

        <TextView
            android:id="@+id/tvQuestion"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Question text goes here"
            android:textSize="18sp"
            android:textStyle="bold"
            android:layout_marginBottom="16dp" />

        <io.github.kbiakov.codeview.CodeView
            android:id="@+id/codeViewQuiz"
            android:layout_width="match_parent"
            android:layout_height="200dp"
            android:layout_marginBottom="16dp"
            android:visibility="gone" />

        <LinearLayout
            android:id="@+id/answersContainer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginBottom="24dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="end">

            <Button
                android:id="@+id/btnSubmit"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Submit Answer"
                android:layout_marginEnd="8dp" />

            <Button
                android:id="@+id/btnNext"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Next Question"
                android:enabled="false" />

        </LinearLayout>

    </LinearLayout>

</ScrollView>
