<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="12dp"
        android:gravity="center_vertical">

        <ImageView
            android:id="@+id/ivProgressIcon"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_marginEnd="12dp"
            android:src="@drawable/ic_token"
            android:contentDescription="Level Icon" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tvProgressTitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Level Title"
                android:textSize="16sp"
                android:textStyle="bold" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginTop="4dp"
                android:gravity="center_vertical">

                <ProgressBar
                    android:id="@+id/progressBarLevel"
                    style="?android:attr/progressBarStyleHorizontal"
                    android:layout_width="0dp"
                    android:layout_height="6dp"
                    android:layout_weight="1"
                    android:max="100"
                    android:progress="0" />

                <TextView
                    android:id="@+id/tvProgressPercentage"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="0%"
                    android:textSize="12sp"
                    android:layout_marginStart="8dp" />

            </LinearLayout>

            <TextView
                android:id="@+id/tvLessonsProgress"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Lessons: 0/0"
                android:textSize="12sp"
                android:layout_marginTop="2dp"
                android:textColor="@color/text_secondary" />

        </LinearLayout>

        <TextView
            android:id="@+id/tvProgressStatus"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Status"
            android:textSize="12sp"
            android:textStyle="bold"
            android:layout_marginStart="8dp" />

    </LinearLayout>

</androidx.cardview.widget.CardView>
