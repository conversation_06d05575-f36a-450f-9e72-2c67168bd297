<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".MainActivity">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:padding="16dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Welcome to Solidity Learning"
            android:textSize="24sp"
            android:textStyle="bold"
            android:textAlignment="center"
            android:layout_marginBottom="8dp" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Master smart contract development through interactive lessons and quizzes"
            android:textSize="16sp"
            android:textAlignment="center"
            android:layout_marginBottom="24dp"
            android:textColor="@color/text_secondary" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerViewLevels"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            tools:listitem="@layout/item_level" />

    </LinearLayout>

    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fabProgress"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="16dp"
        android:src="@drawable/ic_progress"
        android:contentDescription="View Progress" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
