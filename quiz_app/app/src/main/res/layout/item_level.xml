<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/cardLevel"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp">

        <ImageView
            android:id="@+id/ivLevelIcon"
            android:layout_width="64dp"
            android:layout_height="64dp"
            android:layout_marginEnd="16dp"
            android:src="@drawable/ic_token"
            android:contentDescription="Level Icon" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tvLevelTitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Level Title"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="@color/text_primary" />

            <TextView
                android:id="@+id/tvLevelDescription"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Level Description"
                android:textSize="14sp"
                android:textColor="@color/text_secondary"
                android:layout_marginTop="4dp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginTop="8dp"
                android:gravity="center_vertical">

                <ProgressBar
                    android:id="@+id/progressLevel"
                    style="?android:attr/progressBarStyleHorizontal"
                    android:layout_width="0dp"
                    android:layout_height="8dp"
                    android:layout_weight="1"
                    android:max="100"
                    android:progress="0" />

                <TextView
                    android:id="@+id/tvProgress"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="0%"
                    android:textSize="12sp"
                    android:layout_marginStart="8dp" />

            </LinearLayout>

            <TextView
                android:id="@+id/tvStatus"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Status"
                android:textSize="12sp"
                android:textStyle="bold"
                android:layout_marginTop="4dp" />

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>
