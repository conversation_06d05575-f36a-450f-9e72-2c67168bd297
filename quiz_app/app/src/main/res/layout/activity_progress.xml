<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp"
    tools:context=".ProgressActivity">

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        app:cardCornerRadius="8dp"
        app:cardElevation="4dp"
        xmlns:app="http://schemas.android.com/apk/res-auto">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Learning Summary"
                android:textSize="20sp"
                android:textStyle="bold"
                android:textAlignment="center"
                android:layout_marginBottom="12dp" />

            <TextView
                android:id="@+id/tvOverallProgress"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Overall Progress: 0%"
                android:textSize="16sp"
                android:layout_marginBottom="8dp" />

            <TextView
                android:id="@+id/tvCompletedLevels"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Completed Levels: 0/5"
                android:textSize="16sp" />

        </LinearLayout>

    </androidx.cardview.widget.CardView>

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Level Progress"
        android:textSize="18sp"
        android:textStyle="bold"
        android:layout_marginBottom="12dp" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerViewProgress"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        tools:listitem="@layout/item_progress" />

</LinearLayout>
