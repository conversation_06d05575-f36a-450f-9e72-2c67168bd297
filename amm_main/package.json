{"name": "amm", "version": "1.0.0", "description": "", "dependencies": {"@reduxjs/toolkit": "^2.1.0", "@testing-library/jest-dom": "^6.1.6", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.2", "bootstrap": "^5.3.2", "lodash": "^4.17.21", "react": "^18.2.0", "react-apexcharts": "^1.4.1", "react-blockies": "^1.4.1", "react-bootstrap": "^2.9.2", "react-dom": "^18.2.0", "react-redux": "^9.1.0", "react-router-bootstrap": "^0.26.2", "react-router-dom": "^6.21.3", "react-scripts": "5.0.1", "redux-thunk": "^3.1.0", "reselect": "^5.0.1", "web-vitals": "^3.5.1"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "author": "<EMAIL>", "license": "ISC", "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@nomicfoundation/hardhat-toolbox": "^4.0.0", "hardhat": "^2.19.4"}}