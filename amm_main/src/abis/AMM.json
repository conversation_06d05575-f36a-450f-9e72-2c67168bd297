[{"inputs": [{"internalType": "contract Token", "name": "_token1", "type": "address"}, {"internalType": "contract Token", "name": "_token2", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "address", "name": "tokenGive", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "tokenGiveAmount", "type": "uint256"}, {"indexed": false, "internalType": "address", "name": "tokenGet", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "tokenGetAmount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "token1Balance", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "token2Balance", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "<PERSON><PERSON><PERSON>", "type": "event"}, {"inputs": [], "name": "K", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_token1Amount", "type": "uint256"}, {"internalType": "uint256", "name": "_token2Amount", "type": "uint256"}], "name": "addLiquidity", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_token2Amount", "type": "uint256"}], "name": "calculateToken1Deposit", "outputs": [{"internalType": "uint256", "name": "token1Amount", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_token1Amount", "type": "uint256"}], "name": "calculateToken1Swap", "outputs": [{"internalType": "uint256", "name": "token2Amount", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_token1Amount", "type": "uint256"}], "name": "calculateToken2Deposit", "outputs": [{"internalType": "uint256", "name": "token2Amount", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_token2Amount", "type": "uint256"}], "name": "calculateToken2Swap", "outputs": [{"internalType": "uint256", "name": "token1Amount", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_share", "type": "uint256"}], "name": "calculateWithdrawAmount", "outputs": [{"internalType": "uint256", "name": "token1Amount", "type": "uint256"}, {"internalType": "uint256", "name": "token2Amount", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_share", "type": "uint256"}], "name": "removeLiquidity", "outputs": [{"internalType": "uint256", "name": "token1Amount", "type": "uint256"}, {"internalType": "uint256", "name": "token2Amount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "shares", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_token1Amount", "type": "uint256"}], "name": "swapToken1", "outputs": [{"internalType": "uint256", "name": "token2Amount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_token2Amount", "type": "uint256"}], "name": "swapToken2", "outputs": [{"internalType": "uint256", "name": "token1Amount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "token1", "outputs": [{"internalType": "contract Token", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "token1Balance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "token2", "outputs": [{"internalType": "contract Token", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "token2Balance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalShares", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}]